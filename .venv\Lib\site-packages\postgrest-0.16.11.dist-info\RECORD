postgrest-0.16.11.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
postgrest-0.16.11.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
postgrest-0.16.11.dist-info/METADATA,sha256=XFv3urLTcuoXLIkt1CmcviteKuEcpHkYYR_IZuPO3P4,5123
postgrest-0.16.11.dist-info/RECORD,,
postgrest-0.16.11.dist-info/WHEEL,sha256=sP946D7jFCHeNz5Iq4fL4Lu-PrWrFsgfLXbbkciIZwg,88
postgrest/__init__.py,sha256=30MCuUqc_tEQZxJFN01F9DXucq1iMMKDeMoW43TYSwk,950
postgrest/__pycache__/__init__.cpython-313.pyc,,
postgrest/__pycache__/base_client.cpython-313.pyc,,
postgrest/__pycache__/base_request_builder.cpython-313.pyc,,
postgrest/__pycache__/constants.cpython-313.pyc,,
postgrest/__pycache__/deprecated_client.cpython-313.pyc,,
postgrest/__pycache__/deprecated_get_request_builder.cpython-313.pyc,,
postgrest/__pycache__/exceptions.cpython-313.pyc,,
postgrest/__pycache__/types.cpython-313.pyc,,
postgrest/__pycache__/utils.cpython-313.pyc,,
postgrest/__pycache__/version.cpython-313.pyc,,
postgrest/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_async/__pycache__/__init__.cpython-313.pyc,,
postgrest/_async/__pycache__/client.cpython-313.pyc,,
postgrest/_async/__pycache__/request_builder.cpython-313.pyc,,
postgrest/_async/client.py,sha256=5nsCiUKmWRQMrAUxTSVA3WbZzdThoPct19HZYqJsSUo,4023
postgrest/_async/request_builder.py,sha256=1iDZKXLb9bkLltF_Idjp4UsRveHSzLcGirn8cBbbLPc,14147
postgrest/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_sync/__pycache__/__init__.cpython-313.pyc,,
postgrest/_sync/__pycache__/client.cpython-313.pyc,,
postgrest/_sync/__pycache__/request_builder.cpython-313.pyc,,
postgrest/_sync/client.py,sha256=n22MB08cNAa533HH1XiPaufdYxVligp6SLF9vY71Ptg,3977
postgrest/_sync/request_builder.py,sha256=BIqlP96wyMjKWqw1Tcfs-42qvHSZUShSlpXUoNiqnlo,14079
postgrest/base_client.py,sha256=8P-tJk-WmttXrCx_pG5p015DCcpKqpRQqHYh8Pqqp_0,1984
postgrest/base_request_builder.py,sha256=Li8Uyb67gaAtkKIDbjIWw1h128lcQJ0DjDOaBcCeofE,23272
postgrest/constants.py,sha256=VZrlQtgGV-qwcjwqhlJOZBhPHzbSICjDJbabcAlPMUY,153
postgrest/deprecated_client.py,sha256=6sC3m36fiUrwORHYOSyXjUXC21f4BfdTCEMgIedN8qE,416
postgrest/deprecated_get_request_builder.py,sha256=ycFiTJSfO4sWlQGSQMPVWj3oXLQAv1FVIi0vgT8o26A,429
postgrest/exceptions.py,sha256=T4ORME29C_CyIJUCWlDXoS3qSyv7LxMov1xLyMGvfho,1510
postgrest/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
postgrest/types.py,sha256=o1CCqNb7TAfdaCoYBF137kDio37dWszGNRaQJsDhHqY,986
postgrest/utils.py,sha256=_9qLBJvOImBRkln1dJk6k236CcmcYOajhJMS-b5czmw,1152
postgrest/version.py,sha256=fHqvdL7WqRxgzV6L1t-dmfz7sFy_mAl13APktsc4Ymw,54
