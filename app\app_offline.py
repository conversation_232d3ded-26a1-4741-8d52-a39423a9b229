#!/usr/bin/env python3
"""
Offline version of the Flask app for testing
This version works without Supabase connection and includes all necessary API endpoints
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, timedelta
import os
import hashlib
import random
from functools import wraps

print("🚀 Starting Flask App (Offline Mode)")
print("=" * 50)

# Create Flask app
app = Flask(__name__)
app.secret_key = 'test-secret-key-change-in-production'

# Enhanced mock data for testing with Arduino format machine IDs
mock_events = []

# Generate realistic mock data
base_time = datetime.now() - timedelta(days=7)
machine_ids = ['machine_001', 'machine_002', 'machine_003']

for i in range(50):
    machine_id = random.choice(machine_ids)
    start_time = base_time + timedelta(hours=random.randint(0, 168), minutes=random.randint(0, 59))
    duration = random.randint(300, 7200)  # 5 minutes to 2 hours
    end_time = start_time + timedelta(seconds=duration)

    mock_events.append({
        'id': i + 1,
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat(),
        'duration': duration,
        'machine_id': machine_id,
        'created_at': start_time.isoformat()
    })

# Sort events by start_time (newest first)
mock_events.sort(key=lambda x: x['start_time'], reverse=True)

# Mock users with Arduino format machine IDs
users = {
    'admin': {
        'password': hashlib.sha256('admin123'.encode()).hexdigest(),
        'role': 'admin',
        'machine_ids': ['machine_001', 'machine_002', 'machine_003'],
        'machine_name': 'All Machines'
    },
    'pankaj': {
        'password': hashlib.sha256('pankaj123'.encode()).hexdigest(),
        'role': 'admin',
        'machine_ids': ['machine_001', 'machine_002', 'machine_003'],
        'machine_name': 'All Machines'
    },
    'user1': {
        'password': hashlib.sha256('password1'.encode()).hexdigest(),
        'role': 'user',
        'machine_ids': ['machine_001'],
        'machine_name': 'Machine 1'
    }
}

def login_required(f):
    """Decorator to require login for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.path.startswith('/api/'):
                return jsonify({'error': 'Unauthorized', 'message': 'Please login'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_user_machine_filter():
    """Get machine filter for current user"""
    if 'username' not in session:
        return []
    user = users.get(session['username'])
    if not user:
        return []
    return user.get('machine_ids', [])

@app.route('/')
def index():
    if 'username' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        if username in users and users[username]['password'] == password_hash:
            session['username'] = username
            session['role'] = users[username]['role']
            session['machine_ids'] = users[username]['machine_ids']
            session['machine_name'] = users[username]['machine_name']
            flash(f'Welcome, {username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials', 'error')

    return render_template('login.html')

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

@app.route('/events')
@login_required
def events():
    return render_template('events.html')

# API Routes
@app.route('/api/health')
def health():
    return jsonify({
        'status': 'healthy',
        'mode': 'offline_test',
        'timestamp': datetime.now().isoformat(),
        'database': 'mock_data',
        'session_active': 'username' in session,
        'user': session.get('username', 'Not logged in'),
        'supabase_available': False
    })

@app.route('/api/summary')
@login_required
def get_summary():
    try:
        # Get selected machine from query parameter
        selected_machine = request.args.get('machine_id')

        # Get user's machine filter
        machine_ids = get_user_machine_filter()

        # If no specific machine selected, use all accessible machines
        if not selected_machine or selected_machine == 'all':
            machine_filter = machine_ids
        else:
            # Validate user has access to selected machine
            if selected_machine not in machine_ids:
                return jsonify({'error': 'Access denied to selected machine'}), 403
            machine_filter = [selected_machine]

        # Filter events based on machine access
        filtered_events = [e for e in mock_events if e['machine_id'] in machine_filter]

        # Calculate summary
        total_events = len(filtered_events)
        total_runtime = sum(event['duration'] for event in filtered_events)
        last_event = filtered_events[0] if filtered_events else None

        return jsonify({
            'total_events': total_events,
            'total_runtime': total_runtime,
            'last_event': last_event,
            'machine_filter': machine_filter
        }), 200

    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/machine-data')
@login_required
def get_machine_data():
    try:
        # Get selected machine from query parameter
        selected_machine = request.args.get('machine_id')

        # Get user's machine filter
        machine_ids = get_user_machine_filter()

        # If no specific machine selected, use all accessible machines
        if not selected_machine or selected_machine == 'all':
            machine_filter = machine_ids
        else:
            # Validate user has access to selected machine
            if selected_machine not in machine_ids:
                return jsonify({'error': 'Access denied to selected machine'}), 403
            machine_filter = [selected_machine]

        # Filter events based on machine access
        filtered_events = [e for e in mock_events if e['machine_id'] in machine_filter]

        # Limit to recent events (last 50)
        recent_events = filtered_events[:50]

        return jsonify({
            'machine_id': selected_machine or 'all',
            'machine_name': selected_machine or 'All Machines',
            'total_events': len(recent_events),
            'events': recent_events
        }), 200

    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

# Chart API endpoints (mock data)
@app.route('/api/charts/daily-runtime')
@login_required
def get_daily_runtime():
    # Generate mock daily runtime data
    days = []
    data = []
    for i in range(7):
        day = datetime.now() - timedelta(days=i)
        days.append(day.strftime('%Y-%m-%d'))
        data.append(random.randint(4, 12) * 3600)  # 4-12 hours in seconds

    return jsonify({
        'labels': list(reversed(days)),
        'data': list(reversed(data))
    })

@app.route('/api/charts/hourly-distribution')
@login_required
def get_hourly_distribution():
    # Generate mock hourly distribution data
    hours = [f"{i:02d}:00" for i in range(24)]
    data = [random.randint(0, 10) for _ in range(24)]

    return jsonify({
        'labels': hours,
        'data': data
    })

@app.route('/api/charts/duration-distribution')
@login_required
def get_duration_distribution():
    # Generate mock duration distribution data
    ranges = ['0-30min', '30-60min', '1-2hr', '2-4hr', '4hr+']
    data = [random.randint(5, 25) for _ in range(5)]

    return jsonify({
        'labels': ranges,
        'data': data
    })

@app.route('/api/data', methods=['POST'])
def receive_data():
    """Receive data from Arduino (mock endpoint)"""
    try:
        data = request.get_json()
        # In offline mode, just acknowledge receipt
        return jsonify({'message': 'Data received successfully (offline mode)', 'id': random.randint(1000, 9999)}), 200
    except Exception as e:
        return jsonify({'error': 'Failed to save data', 'details': str(e)}), 500

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("🌐 Server will be accessible at:")
    print("   ➤ http://localhost:5000")
    print("   ➤ http://127.0.0.1:5000")
    print("   ➤ http://0.0.0.0:5000")
    print()
    print("🔑 Test Credentials:")
    print("   ➤ admin / admin123")
    print("   ➤ pankaj / pankaj123")
    print("   ➤ user1 / password1")
    print()
    print("📊 Features Available:")
    print("   ➤ Full Dashboard with Charts")
    print("   ➤ Machine Data API")
    print("   ➤ User Authentication")
    print("   ➤ Role-based Access Control")
    print()
    print("⏹️  Press Ctrl+C to stop")
    print("=" * 50)

    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        input("Press Enter to exit...")
