# 🚀 Machine Monitoring Webapp - Deployment Package

## 📦 Package Contents

Your complete webapp deployment package `machine-monitoring-webapp.zip` contains:

### 🎯 Quick Start Files
- **`start.bat`** - Main startup script (JUST DOUBLE-CLICK THIS!)
- **`stop.bat`** - Graceful shutdown script
- **`README.md`** - Complete documentation
- **`DEPLOYMENT_CHECKLIST.md`** - Step-by-step deployment guide

### 🔧 Application Files
- **`app/app.py`** - Main Flask application
- **`app/requirements.txt`** - Python dependencies
- **`app/config.py`** - Configuration settings
- **`app/templates/`** - HTML templates (dashboard, login, etc.)
- **`app/static/`** - CSS, JavaScript, images
- **`app/wsgi.py`** - Production server configuration

### 🔌 Arduino Integration
- **`final_code/final_code.ino`** - Arduino code for data submission

### ⚙️ Optional Files
- **`install_as_service.bat`** - Install as Windows service (optional)

## 🚀 Super Quick Deployment

### Step 1: Extract
Extract `machine-monitoring-webapp.zip` to your server (e.g., `C:\machine-monitoring\`)

### Step 2: Run
Double-click **`start.bat`** - That's it!

### Step 3: Access
Open browser: `http://localhost:5000`
Login: `admin` / `admin123`

## 🌐 Network Access

To access from other devices on your network:

1. **Find your server IP**: Run `ipconfig` in command prompt
2. **Configure firewall**: Allow port 5000
3. **Access from network**: `http://YOUR_SERVER_IP:5000`

### Quick Firewall Command (Run as Administrator):
```cmd
netsh advfirewall firewall add rule name="Machine Monitoring" dir=in action=allow protocol=TCP localport=5000
```

## 📊 Dashboard Features

✅ **Real-time machine monitoring**
✅ **Arduino format support** (machine_001, machine_002, machine_003)
✅ **Dynamic data filtering** by selected machine
✅ **Live charts and analytics**
✅ **Event logging and history**
✅ **Responsive design** (works on mobile)

## 🔐 Default Login Accounts

| Username | Password | Access |
|----------|----------|---------|
| admin | admin123 | All machines |
| pankaj | pankaj123 | All machines |
| abhi | abhi123 | All machines |
| ankur_admin | ankur@2023 | All machines |

## 📡 Arduino Data Submission

Your Arduino should send data to:
```
POST http://YOUR_SERVER_IP:5000/api/data
Content-Type: application/json

{
    "machine_id": "machine_001",
    "start_time": "2025-08-09T10:30:00",
    "end_time": "2025-08-09T10:35:00", 
    "duration": 300
}
```

## 🎛️ Machine Selection

The dashboard now shows **only Arduino format** machines:
- machine_001
- machine_002  
- machine_003

Data counts update automatically based on selected machine:
- **All Machines**: Shows combined data from all machines
- **machine_001**: Shows only machine_001 data
- **machine_002**: Shows only machine_002 data
- **machine_003**: Shows only machine_003 data

## 🔧 System Requirements

- **Windows Server/PC**
- **Python 3.8+** (will be installed automatically if needed)
- **Internet connection** (for database access)
- **Port 5000 available**
- **Minimum 500MB disk space**

## 📈 Current Data Status

Based on your database:
- **machine_001**: 18 events, 949 seconds runtime
- **machine_002**: 4 events
- **machine_003**: 3 events
- **Total**: 25 events across all machines

## 🆘 Troubleshooting

### Server Won't Start
1. Check if Python is installed: `python --version`
2. Run `start.bat` as Administrator
3. Check if port 5000 is available

### Can't Access from Network
1. Check Windows Firewall settings
2. Verify server IP address
3. Test local access first

### Database Issues
1. Check internet connection
2. Verify data in Supabase dashboard
3. Check application logs in `app/logs/`

## 📞 Support

- **Logs**: Check `app/logs/` directory
- **Configuration**: Edit `app/config.py`
- **Arduino Code**: See `final_code/final_code.ino`
- **Documentation**: Read `README.md`

## 🎉 Success Indicators

✅ Server starts without errors
✅ Dashboard loads at http://localhost:5000
✅ Login works with admin/admin123
✅ Machine dropdown shows machine_001, machine_002, machine_003
✅ Data counts change when selecting different machines
✅ Charts display correctly
✅ Arduino can submit data successfully

---

**🎯 Your webapp is ready for production deployment!**

Just extract the zip file and double-click `start.bat` - everything else is automated!
