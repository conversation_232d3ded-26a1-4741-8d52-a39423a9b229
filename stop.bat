@echo off
echo ========================================
echo   Stopping Machine Monitoring System
echo ========================================
echo.

:: Kill any Python processes running the app
echo Stopping Flask application...
taskkill /f /im python.exe 2>nul
taskkill /f /im pythonw.exe 2>nul

:: Kill processes by port (if running on port 5000)
echo Stopping processes on port 5000...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":5000" ^| find "LISTENING"') do (
    echo Killing process ID: %%a
    taskkill /f /pid %%a 2>nul
)

echo.
echo Machine Monitoring System stopped.
echo.
pause
