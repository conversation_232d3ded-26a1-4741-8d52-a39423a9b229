#!/usr/bin/env python3
"""
Quick health test for Machine Monitoring App
"""

import os
import sys
import requests
import time
import subprocess
from threading import Thread

def test_health_endpoint():
    """Test the health endpoint"""
    max_attempts = 10
    for attempt in range(max_attempts):
        try:
            print(f"[{attempt+1}/{max_attempts}] Testing health endpoint...")
            response = requests.get('http://localhost:5000/api/health', timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Health endpoint working!")
                print(f"   Status: {data.get('status')}")
                print(f"   Supabase Available: {data.get('supabase_available')}")
                print(f"   Database Records: {data.get('database_records', 'N/A')}")
                
                if data.get('database_error'):
                    print(f"   ❌ Database Error: {data.get('database_error')}")
                
                return True
            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ⏳ Server not ready yet, waiting...")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        time.sleep(2)
    
    print("❌ Health endpoint test failed after all attempts")
    return False

def main():
    print("🔍 Machine Monitoring Health Test")
    print("=" * 40)
    
    # Check if we're in the right directory
    if os.path.exists('app/app.py'):
        os.chdir('app')
        print("📂 Changed to app directory")
    elif not os.path.exists('app.py'):
        print("❌ app.py not found!")
        return False
    
    print("📍 Current directory:", os.getcwd())
    
    # Test imports first
    try:
        print("📦 Testing imports...")
        from dotenv import load_dotenv
        load_dotenv()
        
        from supabase import create_client
        url = os.getenv('SUPABASE_URL', 'https://zvfltkbciwppawghqpdl.supabase.co')
        key = os.getenv('SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE')
        
        client = create_client(url, key)
        result = client.table('machine_events').select('*').limit(1).execute()
        print(f"✅ Direct Supabase test: {len(result.data) if result.data else 0} records")
        
    except Exception as e:
        print(f"❌ Direct Supabase test failed: {e}")
        return False
    
    # Start the Flask app in background
    print("\n🚀 Starting Flask app...")
    try:
        # Start Flask app as subprocess
        process = subprocess.Popen([
            sys.executable, 'app.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("⏳ Waiting for server to start...")
        time.sleep(5)
        
        # Test health endpoint
        success = test_health_endpoint()
        
        # Stop the process
        process.terminate()
        process.wait(timeout=5)
        
        return success
        
    except Exception as e:
        print(f"❌ Failed to start Flask app: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Health test passed! The app should work correctly.")
    else:
        print("\n❌ Health test failed. Check the errors above.")
    
    input("\nPress Enter to exit...")
