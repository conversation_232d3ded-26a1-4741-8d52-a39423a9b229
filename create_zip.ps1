# PowerShell script to create deployment zip file
Write-Host "Creating Machine Monitoring Webapp Zip File..." -ForegroundColor Green

$source = "."
$destination = "machine-monitoring-webapp.zip"

# Remove existing zip if it exists
if (Test-Path $destination) {
    Remove-Item $destination -Force
    Write-Host "Removed existing zip file" -ForegroundColor Yellow
}

# Define files and folders to include
$includeItems = @(
    "start.bat",
    "stop.bat", 
    "install_as_service.bat",
    "README.md",
    "DEPLOYMENT_CHECKLIST.md",
    "app\app.py",
    "app\config.py",
    "app\requirements.txt",
    "app\wsgi.py",
    "app\service_install.py",
    "app\templates\*",
    "app\static\*",
    "final_code\final_code.ino"
)

# Create temporary directory structure
$tempDir = "temp_webapp"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

# Copy files maintaining directory structure
foreach ($item in $includeItems) {
    $sourcePath = Join-Path $source $item
    if (Test-Path $sourcePath) {
        $relativePath = $item
        $destPath = Join-Path $tempDir $relativePath
        $destDir = Split-Path $destPath -Parent
        
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        
        if ((Get-Item $sourcePath).PSIsContainer) {
            Copy-Item $sourcePath $destDir -Recurse -Force
        } else {
            Copy-Item $sourcePath $destPath -Force
        }
        Write-Host "Copied: $item" -ForegroundColor Cyan
    } else {
        Write-Host "Warning: $item not found" -ForegroundColor Yellow
    }
}

# Create the zip file
Compress-Archive -Path "$tempDir\*" -DestinationPath $destination -Force

# Clean up temp directory
Remove-Item $tempDir -Recurse -Force

# Show result
if (Test-Path $destination) {
    $zipSize = (Get-Item $destination).Length / 1MB
    Write-Host "Successfully created: $destination" -ForegroundColor Green
    Write-Host "Size: $([math]::Round($zipSize, 2)) MB" -ForegroundColor Green
    Write-Host ""
    Write-Host "Deployment Instructions:" -ForegroundColor Yellow
    Write-Host "1. Extract the zip file on your server" -ForegroundColor White
    Write-Host "2. Double-click start.bat to run the webapp" -ForegroundColor White
    Write-Host "3. Access at http://localhost:5000" -ForegroundColor White
    Write-Host "4. Login with admin/admin123" -ForegroundColor White
} else {
    Write-Host "Error: Failed to create zip file" -ForegroundColor Red
}
