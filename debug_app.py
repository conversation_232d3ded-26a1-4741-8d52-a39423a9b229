#!/usr/bin/env python3
"""
Debug script to diagnose Machine Monitoring App issues
"""

import os
import sys
import traceback
from datetime import datetime

print("🔍 Machine Monitoring App Diagnostics")
print("=" * 50)
print(f"Time: {datetime.now()}")
print(f"Python: {sys.version}")
print(f"Working Directory: {os.getcwd()}")
print()

# Test 1: Check if we're in the right directory
print("1. 📁 Directory Check")
if os.path.exists('app.py'):
    print("   ✅ app.py found in current directory")
elif os.path.exists('app/app.py'):
    print("   ✅ app.py found in app/ directory")
    os.chdir('app')
    print(f"   📂 Changed to: {os.getcwd()}")
else:
    print("   ❌ app.py not found!")
    sys.exit(1)

# Test 2: Check environment file
print("\n2. 🔧 Environment Check")
if os.path.exists('.env'):
    print("   ✅ .env file found")
    with open('.env', 'r') as f:
        lines = f.readlines()
        for line in lines:
            if line.strip() and not line.startswith('#'):
                key = line.split('=')[0]
                print(f"   📝 {key}=***")
else:
    print("   ⚠️  .env file not found")

# Test 3: Import required modules
print("\n3. 📦 Import Test")
try:
    import flask
    print(f"   ✅ Flask {flask.__version__}")
    
    from dotenv import load_dotenv
    print("   ✅ python-dotenv")
    
    from supabase import create_client, Client
    print("   ✅ supabase")
    
except ImportError as e:
    print(f"   ❌ Import error: {e}")
    sys.exit(1)

# Test 4: Load environment variables
print("\n4. 🌍 Environment Variables")
try:
    load_dotenv()
    
    supabase_url = os.getenv('SUPABASE_URL', 'https://zvfltkbciwppawghqpdl.supabase.co')
    supabase_key = os.getenv('SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE')
    
    print(f"   ✅ SUPABASE_URL: {supabase_url}")
    print(f"   ✅ SUPABASE_KEY: {supabase_key[:20]}...")
    
except Exception as e:
    print(f"   ❌ Environment error: {e}")

# Test 5: Test Supabase connection
print("\n5. 🗄️  Database Connection Test")
try:
    client = create_client(supabase_url, supabase_key)
    print("   ✅ Supabase client created")
    
    # Test connection
    result = client.table('machine_events').select("*").limit(1).execute()
    print(f"   ✅ Database query successful")
    print(f"   📊 Records found: {len(result.data) if result.data else 0}")
    
    # Test specific machine data
    machine_result = client.table('machine_events').select("*").eq('machine_id', 'machine_001').limit(1).execute()
    print(f"   📊 machine_001 records: {len(machine_result.data) if machine_result.data else 0}")
    
except Exception as e:
    print(f"   ❌ Database connection failed: {e}")
    print(f"   📋 Full error: {traceback.format_exc()}")

# Test 6: Try importing the app
print("\n6. 🚀 Flask App Import Test")
try:
    # Import the app
    import app as flask_app
    print("   ✅ App module imported")
    
    if hasattr(flask_app, 'app'):
        print("   ✅ Flask app object found")
        
        # Check if supabase is initialized
        if hasattr(flask_app, 'supabase') and flask_app.supabase is not None:
            print("   ✅ Supabase client initialized in app")
        else:
            print("   ❌ Supabase client is None in app")
            
    else:
        print("   ❌ Flask app object not found")
        
except Exception as e:
    print(f"   ❌ App import failed: {e}")
    print(f"   📋 Full error: {traceback.format_exc()}")

# Test 7: Test API endpoints manually
print("\n7. 🌐 API Endpoint Test")
try:
    from flask import Flask
    import app as flask_app
    
    # Create test client
    with flask_app.app.test_client() as client:
        # Test health endpoint
        response = client.get('/api/health')
        print(f"   🏥 /api/health: {response.status_code}")
        
        # Test summary endpoint (this might fail due to login requirement)
        response = client.get('/api/summary')
        print(f"   📊 /api/summary: {response.status_code}")
        
except Exception as e:
    print(f"   ❌ API test failed: {e}")

print("\n" + "=" * 50)
print("🏁 Diagnostics Complete!")
print("\n💡 If all tests pass, the app should work correctly.")
print("💡 If any tests fail, check the error messages above.")
