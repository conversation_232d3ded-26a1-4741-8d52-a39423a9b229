#!/usr/bin/env python3
"""
Simplified Machine Monitoring System
"""

import os
import logging
from datetime import datetime, timezone, timedelta
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from dotenv import load_dotenv
from supabase import create_client, Client
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Create Flask app
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-change-this-in-production')

# IST timezone
IST = timezone(timedelta(hours=5, minutes=30))

def get_ist_now():
    """Get current time in IST"""
    return datetime.now(IST)

# Initialize Supabase
supabase = None
try:
    SUPABASE_URL = os.getenv("SUPABASE_URL", 'https://zvfltkbciwppawghqpdl.supabase.co')
    SUPABASE_KEY = os.getenv("SUPABASE_KEY", 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE')
    
    logger.info(f"Connecting to Supabase at {SUPABASE_URL}")
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Test connection
    result = supabase.table('machine_events').select("*").limit(1).execute()
    logger.info(f"✅ Supabase connected successfully. Found {len(result.data) if result.data else 0} records")
    
except Exception as e:
    logger.error(f"❌ Supabase initialization failed: {str(e)}")
    supabase = None

# User authentication
USERS = {
    'admin': {
        'password': hashlib.sha256('admin123'.encode()).hexdigest(),
        'machine_ids': ['machine_001', 'machine_002', 'machine_003'],
        'role': 'admin'
    }
}

def login_required(f):
    """Decorator to require login"""
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Routes
@app.route('/')
def index():
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = USERS.get(username)
        if user and user['password'] == hashlib.sha256(password.encode()).hexdigest():
            session['username'] = username
            session['role'] = user['role']
            return redirect(url_for('dashboard'))
        else:
            return render_template('login.html', error='Invalid credentials')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get machine options
    machine_options = [
        {'id': 'machine_001', 'name': 'machine_001'},
        {'id': 'machine_002', 'name': 'machine_002'},
        {'id': 'machine_003', 'name': 'machine_003'}
    ]
    
    return render_template('dashboard.html', 
                         now=get_ist_now(),
                         machine_options=machine_options,
                         selected_machine='machine_001')

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    health_status = {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'session_active': 'username' in session,
        'user': session.get('username', 'Not logged in'),
        'supabase_available': supabase is not None
    }
    
    try:
        if supabase:
            test_response = supabase.table('machine_events').select("*").limit(1).execute()
            health_status['database_records'] = len(test_response.data) if test_response.data else 0
            health_status['database_status'] = 'connected'
        else:
            health_status['database_status'] = 'not_connected'
    except Exception as e:
        health_status['database_error'] = str(e)
        health_status['database_status'] = 'error'
    
    return jsonify(health_status), 200

@app.route('/api/summary')
@login_required
def get_summary():
    """Get summary data"""
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    try:
        # Get selected machine
        selected_machine = request.args.get('machine_id', 'machine_001')
        
        # Get events for the selected machine
        query = supabase.table('machine_events').select("*")
        if selected_machine != 'all':
            query = query.eq('machine_id', selected_machine)
        
        result = query.execute()
        events = result.data if result.data else []
        
        # Calculate summary
        total_events = len(events)
        total_runtime = sum(event.get('duration', 0) for event in events)
        last_event = events[0] if events else None
        
        return jsonify({
            'total_events': total_events,
            'total_runtime': total_runtime,
            'last_event': last_event,
            'machine_filter': [selected_machine]
        }), 200
        
    except Exception as e:
        logger.error(f"Summary API error: {e}")
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/machine-data')
@login_required
def get_machine_data():
    """Get machine data"""
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    try:
        # Get selected machine
        selected_machine = request.args.get('machine_id', 'machine_001')
        
        # Get events for the selected machine
        query = supabase.table('machine_events').select("*")
        if selected_machine != 'all':
            query = query.eq('machine_id', selected_machine)
        
        result = query.order('start_time', desc=True).limit(50).execute()
        events = result.data if result.data else []
        
        return jsonify({
            'machine_id': selected_machine,
            'machine_name': selected_machine,
            'total_events': len(events),
            'events': events
        }), 200
        
    except Exception as e:
        logger.error(f"Machine data API error: {e}")
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/data', methods=['POST'])
def receive_data():
    """Receive data from Arduino"""
    if supabase is None:
        return jsonify({'error': 'Database not available'}), 500
    
    try:
        data = request.get_json()
        
        # Insert data into Supabase
        result = supabase.table('machine_events').insert(data).execute()
        
        return jsonify({'message': 'Data received successfully', 'id': result.data[0]['id'] if result.data else None}), 200
        
    except Exception as e:
        logger.error(f"Data receive error: {e}")
        return jsonify({'error': 'Failed to save data', 'details': str(e)}), 500

if __name__ == '__main__':
    logger.info("Starting Machine Monitoring System...")
    logger.info(f"Supabase available: {supabase is not None}")
    
    # Run the app
    app.run(host='0.0.0.0', port=5000, debug=True)
