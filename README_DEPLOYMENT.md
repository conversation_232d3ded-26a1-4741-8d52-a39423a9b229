# Machine Monitoring System - Deployment Guide

## 🚀 Quick Start

### Local Testing
1. **Double-click** `start_server.bat` 
2. **Or run**: `python start_server.py`
3. **Open browser**: http://localhost:5000
4. **Login**: admin/admin123 or pankaj/pankaj123

### Production Deployment
1. **Run**: `python deploy_production.py`
2. **Navigate to**: `C:\inetpub\wwwroot\machine-monitoring\`
3. **Double-click**: `start_production.bat`
4. **Access**: http://**************:5000

## 📋 System Requirements

- **Python 3.7+**
- **Flask 3.1.0+**
- **Internet connection** (for Supabase, optional)

## 🔧 Installation Options

### Option 1: Automatic Setup (Recommended)
```bash
# For local testing
python start_server.py

# For production deployment
python deploy_production.py
```

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run locally
cd app
python app_offline.py

# Run production
cd app
python app_production.py
```

## 🌐 Server Configurations

### Local Development
- **Host**: localhost
- **Port**: 5000
- **Mode**: Offline (mock data)
- **Debug**: Enabled

### Production Server
- **Host**: **************
- **Port**: 5000
- **Mode**: Production (Supabase + fallback)
- **Debug**: Disabled

## 👥 User Accounts

| Username | Password | Role | Access |
|----------|----------|------|--------|
| admin | admin123 | Admin | All machines |
| pankaj | pankaj123 | Admin | All machines |
| user1 | password1 | User | Machine 1 only |
| user2 | password2 | User | Machine 2 only |
| user3 | password3 | User | Machine 3 only |

## 📊 Features

### ✅ Working Features
- **User Authentication** with role-based access
- **Dashboard** with real-time data
- **Machine Monitoring** (machine_001, machine_002, machine_003)
- **Charts & Analytics** (daily runtime, hourly distribution)
- **Event Logging** and history
- **API Endpoints** for Arduino integration
- **Responsive Design** for mobile/desktop

### 🔄 Data Sources
- **Production**: Supabase database (with offline fallback)
- **Local**: Mock data for testing
- **Arduino**: POST to `/api/data` endpoint

## 🛠️ Troubleshooting

### Common Issues

#### 1. "Module not found" errors
```bash
pip install flask supabase python-dotenv
```

#### 2. Port 5000 already in use
```bash
# Kill existing process
netstat -ano | findstr :5000
taskkill /PID <PID_NUMBER> /F

# Or use different port
python start_server.py 5001
```

#### 3. Database connection issues
- App automatically falls back to offline mode
- Check Supabase credentials in environment variables
- Verify internet connection

#### 4. Permission errors on production server
```bash
# Run as administrator
# Or check folder permissions for C:\inetpub\wwwroot\
```

### 🔍 Health Check
Visit `/api/health` to check system status:
- http://localhost:5000/api/health
- http://**************:5000/api/health

## 📁 File Structure

```
machine-monitoring/
├── app/
│   ├── app_offline.py          # Local testing app
│   ├── app_production.py       # Production app
│   ├── templates/              # HTML templates
│   └── static/                 # CSS, JS, images
├── start_server.py             # Auto-start script
├── start_server.bat            # Windows batch file
├── deploy_production.py        # Production deployment
├── requirements.txt            # Python dependencies
└── README_DEPLOYMENT.md        # This file
```

## 🔧 Advanced Configuration

### Environment Variables
```bash
# Optional environment variables
export SUPABASE_URL="your-supabase-url"
export SUPABASE_KEY="your-supabase-key"
export SECRET_KEY="your-secret-key"
export PORT=5000
export HOST=0.0.0.0
export DEBUG=False
```

### Custom Port
```bash
# Start on custom port
python start_server.py 8080
```

### Production Service
For running as Windows service, use:
```bash
python C:\inetpub\wwwroot\machine-monitoring\run_as_service.py
```

## 📞 Support

### Quick Fixes
1. **Restart the server**: Stop (Ctrl+C) and restart
2. **Clear browser cache**: Ctrl+F5 or incognito mode
3. **Check logs**: Look for error messages in terminal
4. **Verify credentials**: Use admin/admin123 for testing

### API Endpoints
- `GET /api/health` - System health check
- `GET /api/summary` - Dashboard summary data
- `GET /api/machine-data` - Machine events data
- `POST /api/data` - Arduino data submission

### URLs
- **Dashboard**: http://**************:5000/dashboard
- **Login**: http://**************:5000/login
- **Events**: http://**************:5000/events

## ✅ Deployment Checklist

- [ ] Python 3.7+ installed
- [ ] Dependencies installed (`pip install -r requirements.txt`)
- [ ] Production files copied to `C:\inetpub\wwwroot\machine-monitoring\`
- [ ] Server started successfully
- [ ] Health check returns 200 OK
- [ ] Login works with test credentials
- [ ] Dashboard loads without errors
- [ ] API endpoints respond correctly

---

**🎉 Your Machine Monitoring System is ready!**

Access it at: **http://**************:5000**
