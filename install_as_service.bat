@echo off
echo ========================================
echo   Install as Windows Service (Optional)
echo ========================================
echo.
echo This will install the Machine Monitoring System as a Windows Service
echo so it starts automatically when the server boots.
echo.
echo WARNING: This requires Administrator privileges
echo.
pause

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

:: Navigate to app directory
cd /d "%~dp0app"

:: Check if virtual environment exists
if not exist "venv" (
    echo ERROR: Virtual environment not found
    echo Please run start.bat first to set up the environment
    pause
    exit /b 1
)

:: Activate virtual environment and install service dependencies
call venv\Scripts\activate.bat
pip install pywin32

:: Create service installation script
echo Creating service installer...
python service_install.py

echo.
echo Service installation completed!
echo.
echo To manage the service:
echo - Start: net start MachineMonitoring
echo - Stop: net stop MachineMonitoring
echo - Remove: python service_install.py remove
echo.
pause
