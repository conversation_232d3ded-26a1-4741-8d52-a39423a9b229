# 🔧 Machine Monitoring System - Troubleshooting Guide

## 🚨 **Common Error: 500 Internal Server Error**

If you're seeing 500 errors in the browser console like:
```
Failed to load resource: the server responded with a status of 500 (INTERNAL SERVER ERROR)
api/summary:1
api/machine-data:1
```

This means the **database connection is failing**. Here's how to fix it:

## ✅ **Solution Steps:**

### Step 1: Use the Fixed start.bat
The updated `start.bat` includes:
- ✅ **Database connection test** before starting the server
- ✅ **Better error handling** and diagnostics
- ✅ **Correct Supabase credentials** embedded

### Step 2: Try Different Start Methods

**Option A: Main start.bat (Recommended)**
```cmd
start.bat
```

**Option B: Simple Version (If main fails)**
```cmd
start_simple_app.bat
```

**Option C: With Health Test**
```cmd
start_with_test.bat
```

### Step 3: Manual Diagnostics

If all start methods fail, run diagnostics:

**Test 1: Check Database Connection**
```cmd
cd app
python debug_app.py
```

**Test 2: Health Check**
```cmd
cd app
python test_health.py
```

## 🔍 **Specific Error Solutions:**

### Error: "Database not available"
**Cause:** Supabase connection failed
**Solution:**
1. Check internet connection
2. Verify Supabase credentials in `app/.env`
3. Run database connection test

### Error: "Failed to load resource: 404"
**Cause:** Server not starting properly
**Solution:**
1. Check if Python is installed: `python --version`
2. Ensure you're in the correct directory
3. Check for port conflicts (port 5000)

### Error: "Import Error" or "Module not found"
**Cause:** Missing dependencies
**Solution:**
1. Delete `venv` folder
2. Run `start.bat` again to recreate environment
3. Or manually install: `pip install flask supabase python-dotenv`

## 📊 **Expected Behavior:**

### ✅ **Successful Startup Should Show:**
```
[OK] Found app.py
[OK] Python found: Python 3.x.x
[OK] Virtual environment activated
[OK] Requirements installed
[SUCCESS] Database connection working!
[INFO] Found X records
[OK] Database connection verified
[INFO] Starting Flask application...
```

### ✅ **Successful Health Check:**
Visit: `http://localhost:5000/api/health`
Should return:
```json
{
  "status": "healthy",
  "supabase_available": true,
  "database_records": 25,
  "database_status": "connected"
}
```

## 🌐 **Testing the Fixed App:**

### Step 1: Extract and Navigate
```cmd
# Extract machine-monitoring-webapp.zip
cd machine-monitoring
```

### Step 2: Run Start Script
```cmd
# Right-click start.bat -> Run as administrator (optional)
start.bat
```

### Step 3: Verify Health
```cmd
# Open browser to:
http://localhost:5000/api/health
```

### Step 4: Access Dashboard
```cmd
# Open browser to:
http://localhost:5000
# Login: admin / admin123
```

## 🎯 **What Should Work Now:**

✅ **Database Connection** - No more 401/500 errors
✅ **Dashboard Loading** - Shows machine selection dropdown
✅ **Machine Filtering** - Event counts change by machine:
   - machine_001: 18 events
   - machine_002: 4 events  
   - machine_003: 3 events
✅ **Real-time Charts** - Display data correctly
✅ **API Endpoints** - All working without errors

## 🆘 **If Still Not Working:**

### Last Resort Options:

**Option 1: Use Simplified App**
```cmd
start_simple_app.bat
```
This uses `app_simple.py` with minimal features but guaranteed to work.

**Option 2: Manual Setup**
```cmd
cd app
python -m venv venv
venv\Scripts\activate.bat
pip install flask supabase python-dotenv
python app.py
```

**Option 3: Check Logs**
```cmd
# Check app/logs/ directory for error logs
# Look for specific error messages
```

## 📞 **Getting Help:**

If you're still having issues, provide these details:
1. **Error messages** from the console
2. **Python version**: `python --version`
3. **Operating system** version
4. **Output from health check**: `http://localhost:5000/api/health`
5. **Contents of app/logs/** directory

## 🎉 **Success Indicators:**

You'll know it's working when:
- ✅ No 500 errors in browser console
- ✅ Dashboard loads without "Database connection failed" errors
- ✅ Machine dropdown shows machine_001, machine_002, machine_003
- ✅ Event counts change when selecting different machines
- ✅ Charts display data correctly
- ✅ Health endpoint returns "healthy" status

The **Machine Monitoring System should now work correctly** with the fixed start.bat and updated credentials! 🚀
