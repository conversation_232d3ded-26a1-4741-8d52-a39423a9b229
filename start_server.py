#!/usr/bin/env python3
"""
Server startup script for Machine Monitoring System
Automatically detects environment and starts appropriate app
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['flask', 'supabase']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ All packages installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False
    
    return True

def detect_environment():
    """Detect if we're running locally or on production server"""
    # Check if we're on the production server
    production_indicators = [
        os.path.exists('C:\\inetpub\\wwwroot'),  # IIS directory
        '**************' in os.environ.get('COMPUTERNAME', ''),
        '**************' in os.environ.get('COMPUTERNAME', ''),
    ]
    
    if any(production_indicators):
        return 'production'
    else:
        return 'local'

def start_app(environment='local', port=5000):
    """Start the appropriate Flask app"""
    
    print("🚀 Machine Monitoring System Startup")
    print("=" * 50)
    print(f"Environment: {environment}")
    print(f"Port: {port}")
    print()
    
    # Change to app directory
    app_dir = Path(__file__).parent / 'app'
    os.chdir(app_dir)
    
    if environment == 'production':
        # Use production app
        app_file = 'app_production.py'
        print("🏭 Starting Production Server...")
    else:
        # Use offline app for local testing
        app_file = 'app_offline.py'
        print("🧪 Starting Local Test Server...")
    
    # Set environment variables
    os.environ['PORT'] = str(port)
    os.environ['HOST'] = '0.0.0.0'
    
    if environment == 'production':
        os.environ['DEBUG'] = 'False'
    else:
        os.environ['DEBUG'] = 'True'
    
    try:
        # Start the Flask app
        subprocess.run([sys.executable, app_file], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting server: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def main():
    """Main startup function"""
    print("🔍 Checking system requirements...")
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed. Please install required packages manually.")
        input("Press Enter to exit...")
        return
    
    # Detect environment
    environment = detect_environment()
    
    # Get port from command line or use default
    port = 5000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port number: {sys.argv[1]}. Using default port 5000.")
    
    # Start the app
    start_app(environment, port)

if __name__ == '__main__':
    main()
