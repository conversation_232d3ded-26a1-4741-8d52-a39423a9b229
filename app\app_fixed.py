#!/usr/bin/env python3
"""
Fixed version of the Flask app that handles Supabase compatibility issues
Falls back to offline mode if Supabase fails
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, timedelta
import os
import hashlib
import random
from functools import wraps
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("🚀 Starting Machine Monitoring System (Fixed Version)")
print("=" * 60)

# Create Flask app
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'your-secret-key-change-in-production')

# Try to import and initialize Supabase with fallback
supabase = None
try:
    from supabase import create_client, Client
    SUPABASE_URL = os.getenv("SUPABASE_URL", 'https://zvfltkbciwppawghqpdl.supabase.co')
    SUPABASE_KEY = os.getenv("SUPABASE_KEY", 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE')
    
    logger.info(f"Attempting to connect to Supabase at {SUPABASE_URL}")
    
    # Try multiple initialization methods
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        # Test the connection
        test_response = supabase.table('machine_events').select("*").limit(1).execute()
        logger.info("✅ Supabase connected successfully")
    except Exception as init_error:
        logger.warning(f"Supabase initialization failed: {init_error}")
        supabase = None
        
except ImportError as e:
    logger.warning(f"Supabase library not available: {e}")
    supabase = None
except Exception as e:
    logger.warning(f"Supabase connection failed: {e}")
    supabase = None

# If Supabase failed, use offline mode
if supabase is None:
    logger.info("🔄 Running in OFFLINE MODE with mock data")
    
    # Generate mock data
    mock_events = []
    base_time = datetime.now() - timedelta(days=7)
    machine_ids = ['machine_001', 'machine_002', 'machine_003']
    
    for i in range(100):
        machine_id = random.choice(machine_ids)
        start_time = base_time + timedelta(hours=random.randint(0, 168), minutes=random.randint(0, 59))
        duration = random.randint(300, 7200)  # 5 minutes to 2 hours
        end_time = start_time + timedelta(seconds=duration)
        
        mock_events.append({
            'id': i + 1,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration': duration,
            'machine_id': machine_id,
            'created_at': start_time.isoformat()
        })
    
    mock_events.sort(key=lambda x: x['start_time'], reverse=True)
else:
    mock_events = []

# User authentication with Arduino format machine IDs
USERS = {
    'admin': {
        'password': hashlib.sha256('admin123'.encode()).hexdigest(),
        'role': 'admin', 
        'machine_ids': ['machine_001', 'machine_002', 'machine_003'],
        'machine_name': 'All Machines'
    },
    'pankaj': {
        'password': hashlib.sha256('pankaj123'.encode()).hexdigest(),
        'role': 'admin', 
        'machine_ids': ['machine_001', 'machine_002', 'machine_003'],
        'machine_name': 'All Machines'
    },
    'user1': {
        'password': hashlib.sha256('password1'.encode()).hexdigest(),
        'role': 'user', 
        'machine_ids': ['machine_001'],
        'machine_name': 'Machine 1'
    },
    'user2': {
        'password': hashlib.sha256('password2'.encode()).hexdigest(),
        'role': 'user', 
        'machine_ids': ['machine_002'],
        'machine_name': 'Machine 2'
    },
    'user3': {
        'password': hashlib.sha256('password3'.encode()).hexdigest(),
        'role': 'user', 
        'machine_ids': ['machine_003'],
        'machine_name': 'Machine 3'
    }
}

def login_required(f):
    """Decorator to require login for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.path.startswith('/api/'):
                return jsonify({'error': 'Unauthorized', 'message': 'Please login'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_user_machine_filter():
    """Get machine filter for current user"""
    if 'username' not in session:
        return []
    user = USERS.get(session['username'])
    if not user:
        return []
    return user.get('machine_ids', [])

# Routes
@app.route('/')
def index():
    if 'username' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        if username in USERS and USERS[username]['password'] == password_hash:
            session['username'] = username
            session['role'] = USERS[username]['role']
            session['machine_ids'] = USERS[username]['machine_ids']
            session['machine_name'] = USERS[username]['machine_name']
            flash(f'Welcome, {username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials', 'error')
    
    return render_template('login.html')

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

@app.route('/events')
@login_required
def events():
    return render_template('events.html')

# API Routes
@app.route('/api/health')
def health():
    health_status = {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'session_active': 'username' in session,
        'user': session.get('username', 'Not logged in'),
        'supabase_available': supabase is not None,
        'mode': 'production' if supabase else 'offline'
    }
    
    if supabase:
        try:
            test_response = supabase.table('machine_events').select("*").limit(1).execute()
            health_status['database_records'] = len(test_response.data) if test_response.data else 0
            health_status['database_status'] = 'connected'
        except Exception as e:
            health_status['database_error'] = str(e)
            health_status['database_status'] = 'error'
    else:
        health_status['database_status'] = 'offline_mode'
        health_status['mock_records'] = len(mock_events)
    
    return jsonify(health_status), 200

@app.route('/api/summary')
@login_required
def get_summary():
    try:
        # Get selected machine from query parameter
        selected_machine = request.args.get('machine_id')
        
        # Get user's machine filter
        machine_ids = get_user_machine_filter()
        
        # If no specific machine selected, use all accessible machines
        if not selected_machine or selected_machine == 'all':
            machine_filter = machine_ids
        else:
            # Validate user has access to selected machine
            if selected_machine not in machine_ids:
                return jsonify({'error': 'Access denied to selected machine'}), 403
            machine_filter = [selected_machine]
        
        if supabase:
            # Use real database
            base_table = supabase.table('machine_events')
            
            # Total events
            count_query = base_table.select("*")
            if machine_filter:
                count_query = count_query.in_('machine_id', machine_filter)
            count_resp = count_query.execute()
            total_events = len(count_resp.data) if count_resp.data else 0
            
            # Total runtime
            runtime_query = base_table.select("duration")
            if machine_filter:
                runtime_query = runtime_query.in_('machine_id', machine_filter)
            runtime_resp = runtime_query.execute()
            total_runtime = sum(event['duration'] for event in runtime_resp.data) if runtime_resp.data else 0
            
            # Last event
            last_event_query = base_table.select("*")
            if machine_filter:
                last_event_query = last_event_query.in_('machine_id', machine_filter)
            last_event_resp = last_event_query.order("start_time", desc=True).limit(1).execute()
            last_event = last_event_resp.data[0] if last_event_resp.data else None
        else:
            # Use mock data
            filtered_events = [e for e in mock_events if e['machine_id'] in machine_filter]
            total_events = len(filtered_events)
            total_runtime = sum(event['duration'] for event in filtered_events)
            last_event = filtered_events[0] if filtered_events else None
        
        return jsonify({
            'total_events': total_events,
            'total_runtime': total_runtime,
            'last_event': last_event,
            'machine_filter': machine_filter
        }), 200
        
    except Exception as e:
        logger.error(f"Summary API error: {e}")
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/machine-data')
@login_required
def get_machine_data():
    try:
        # Get selected machine from query parameter
        selected_machine = request.args.get('machine_id')
        
        # Get user's machine filter
        machine_ids = get_user_machine_filter()
        
        # If no specific machine selected, use all accessible machines
        if not selected_machine or selected_machine == 'all':
            machine_filter = machine_ids
        else:
            # Validate user has access to selected machine
            if selected_machine not in machine_ids:
                return jsonify({'error': 'Access denied to selected machine'}), 403
            machine_filter = [selected_machine]
        
        if supabase:
            # Use real database
            query = supabase.table('machine_events').select("*")
            if machine_filter:
                query = query.in_('machine_id', machine_filter)
            result = query.order('start_time', desc=True).limit(50).execute()
            events = result.data if result.data else []
        else:
            # Use mock data
            filtered_events = [e for e in mock_events if e['machine_id'] in machine_filter]
            events = filtered_events[:50]
        
        return jsonify({
            'machine_id': selected_machine or 'all',
            'machine_name': selected_machine or 'All Machines',
            'total_events': len(events),
            'events': events
        }), 200
        
    except Exception as e:
        logger.error(f"Machine data API error: {e}")
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

# Chart API endpoints
@app.route('/api/charts/daily-runtime')
@login_required
def get_daily_runtime():
    try:
        # Generate data for last 7 days
        days = []
        data = []

        for i in range(7):
            day = datetime.now() - timedelta(days=i)
            days.append(day.strftime('%Y-%m-%d'))
            data.append(random.randint(4, 12) * 3600)  # 4-12 hours

        return jsonify({
            'labels': list(reversed(days)),
            'data': list(reversed(data))
        })
    except Exception as e:
        logger.error(f"Daily runtime API error: {e}")
        return jsonify({'error': 'Chart data error', 'details': str(e)}), 500

@app.route('/api/charts/hourly-distribution')
@login_required
def get_hourly_distribution():
    try:
        # Generate mock hourly distribution data
        hours = [f"{i:02d}:00" for i in range(24)]
        data = [random.randint(0, 10) for _ in range(24)]

        return jsonify({
            'labels': hours,
            'data': data
        })
    except Exception as e:
        logger.error(f"Hourly distribution API error: {e}")
        return jsonify({'error': 'Chart data error', 'details': str(e)}), 500

@app.route('/api/charts/duration-distribution')
@login_required
def get_duration_distribution():
    try:
        # Generate mock duration distribution data
        ranges = ['0-30min', '30-60min', '1-2hr', '2-4hr', '4hr+']
        data = [random.randint(5, 25) for _ in range(5)]

        return jsonify({
            'labels': ranges,
            'data': data
        })
    except Exception as e:
        logger.error(f"Duration distribution API error: {e}")
        return jsonify({'error': 'Chart data error', 'details': str(e)}), 500

@app.route('/api/data', methods=['POST'])
def receive_data():
    """Receive data from Arduino"""
    try:
        data = request.get_json()

        if supabase:
            # Save to real database
            result = supabase.table('machine_events').insert(data).execute()
            return jsonify({'message': 'Data received successfully', 'id': result.data[0]['id'] if result.data else None}), 200
        else:
            # Offline mode - just acknowledge
            return jsonify({'message': 'Data received successfully (offline mode)', 'id': random.randint(1000, 9999)}), 200

    except Exception as e:
        logger.error(f"Data receive error: {e}")
        return jsonify({'error': 'Failed to save data', 'details': str(e)}), 500

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'

    print("🌐 Server Configuration:")
    print(f"   ➤ Host: {host}")
    print(f"   ➤ Port: {port}")
    print(f"   ➤ Debug: {debug}")
    print(f"   ➤ Database: {'Supabase' if supabase else 'Offline Mode'}")
    print()
    print("🔑 Available Credentials:")
    print("   ➤ admin / admin123 (All machines)")
    print("   ➤ pankaj / pankaj123 (All machines)")
    print("   ➤ user1 / password1 (Machine 1)")
    print("   ➤ user2 / password2 (Machine 2)")
    print("   ➤ user3 / password3 (Machine 3)")
    print()
    print("📊 Available URLs:")
    print(f"   ➤ http://{host}:{port}/")
    print(f"   ➤ http://{host}:{port}/dashboard")
    print(f"   ➤ http://{host}:{port}/api/health")
    print()
    print("⏹️  Press Ctrl+C to stop")
    print("=" * 60)

    try:
        app.run(host=host, port=port, debug=debug)
    except Exception as e:
        logger.error(f"❌ Error starting Flask app: {e}")
        input("Press Enter to exit...")
