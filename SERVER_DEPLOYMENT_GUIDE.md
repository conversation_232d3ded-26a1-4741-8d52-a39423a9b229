# 🚀 Machine Monitoring System - Server Deployment Guide

## 📋 Quick Deployment Steps for Your Production Server

### Step 1: Upload and Extract
1. Upload `machine-monitoring-webapp.zip` to your server (**************)
2. Extract to: `C:\inetpub\wwwroot\machine-monitoring\`
3. Ensure the folder structure looks like:
   ```
   C:\inetpub\wwwroot\machine-monitoring\
   ├── start.bat
   ├── stop.bat
   ├── app\
   │   ├── app.py
   │   ├── requirements.txt
   │   ├── templates\
   │   └── static\
   └── final_code\
   ```

### Step 2: Deploy
1. **Right-click `start.bat`** → **"Run as administrator"**
2. The script will automatically:
   - Create virtual environment
   - Install all dependencies
   - Start two server instances
   - Configure firewall rules
   - Test connectivity

### Step 3: Verify Deployment
After running start.bat, you should see:
- ✅ Two server windows open (Primary: Port 5000, Backup: Port 9090)
- ✅ Servers accessible at:
  - **Primary**: http://**************:5000
  - **Backup**: http://**************:9090
  - **Local**: http://localhost:5000 and http://localhost:9090

## 🌐 Access URLs

### For Users/Dashboard Access:
- **Primary Server**: http://**************:5000
- **Backup Server**: http://**************:9090

### For Arduino Data Submission:
```cpp
// Primary server configuration
String serverURL = "**************";
int serverPort = 5000;
String endpoint = "/api/data";

// Backup server configuration (if primary fails)
String backupURL = "**************";
int backupPort = 9090;
```

## 🔑 Login Credentials

| Username | Password | Access Level |
|----------|----------|--------------|
| admin | admin123 | Full Admin |
| pankaj | pankaj123 | Full Admin |
| abhi | abhi123 | Full Admin |
| ankur_admin | ankur@2023 | Full Admin |

## 🤖 Arduino Integration

### Machine IDs (Arduino Format)
Your dashboard now shows only:
- **machine_001** (18 events in database)
- **machine_002** (4 events in database)  
- **machine_003** (3 events in database)

### Data Submission Format
```json
POST http://**************:5000/api/data
Content-Type: application/json

{
    "machine_id": "machine_001",
    "start_time": "2025-08-09T10:30:00",
    "end_time": "2025-08-09T10:35:00",
    "duration": 300
}
```

## 🎯 Key Features

✅ **Arduino Format Display**: Only shows machine_001, machine_002, machine_003
✅ **Dynamic Data Filtering**: Event counts change based on selected machine
✅ **Dual Server Setup**: Primary (5000) + Backup (9090) for redundancy
✅ **Production Ready**: Uses Waitress WSGI server
✅ **Auto-Firewall**: Automatically configures Windows Firewall
✅ **Real-time Dashboard**: Live charts and monitoring
✅ **Mobile Responsive**: Works on all devices

## 🔧 Server Management

### Start Servers
```cmd
# Right-click and "Run as administrator"
start.bat
```

### Stop Servers
```cmd
# Right-click and "Run as administrator"  
stop.bat
```

### Check Status
- Visit: http://localhost:5000/api/health
- Check server windows for any errors
- View logs in: `app\logs\`

## 🌐 Network Configuration

### Firewall Rules (Auto-configured)
- Port 5000: Machine Monitoring Port 5000
- Port 9090: Machine Monitoring Port 9090

### Router Configuration (if needed)
Ensure your router forwards:
- External **************:5000 → Server Port 5000
- External **************:9090 → Server Port 9090

## 📊 Dashboard Usage

### Machine Selection
1. Login to dashboard
2. Use dropdown to select:
   - **All Machines**: Shows combined data (25 events total)
   - **machine_001**: Shows only machine_001 data (18 events)
   - **machine_002**: Shows only machine_002 data (4 events)
   - **machine_003**: Shows only machine_003 data (3 events)

### Real-time Updates
- Dashboard auto-refreshes every 30 seconds
- Charts update dynamically based on selected machine
- Event counts change instantly when switching machines

## 🆘 Troubleshooting

### Servers Won't Start
1. Ensure running as Administrator
2. Check if Python is installed: `python --version`
3. Check port availability: `netstat -an | find "5000"`
4. Review error messages in server windows

### External Access Issues
1. Test local access first: http://localhost:5000
2. Check Windows Firewall settings
3. Verify router port forwarding
4. Test with: `curl http://**************:5000`

### Arduino Connection Issues
1. Verify server is running: http://**************:5000/api/health
2. Check Arduino code uses correct URL and port
3. Test API endpoint manually with Postman/curl
4. Check server logs for incoming requests

## 📈 Monitoring

### Health Checks
- **Primary**: http://**************:5000/api/health
- **Backup**: http://**************:9090/api/health

### Log Files
- Application logs: `app\logs\app.log`
- Server output: Check server windows

### Database Status
- Current data: 25 total events across all machines
- Supabase connection: Embedded and configured

## 🎉 Success Indicators

After deployment, verify:
- ✅ Both server windows are running without errors
- ✅ Dashboard loads at http://**************:5000
- ✅ Login works with admin/admin123
- ✅ Machine dropdown shows only machine_001, machine_002, machine_003
- ✅ Event counts change when selecting different machines
- ✅ Charts display data correctly
- ✅ Arduino can submit data to /api/data endpoint

---

**🎯 Your Machine Monitoring System is now production-ready with redundant servers and Arduino format support!**
