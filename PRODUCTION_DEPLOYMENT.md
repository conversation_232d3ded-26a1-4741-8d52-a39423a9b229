# 🚀 Production Deployment Guide - Server **************

## 📋 **Server Information**
- **Production URL**: http://**************:5000
- **Dashboard URL**: http://**************:5000/dashboard
- **Server IP**: **************
- **Port**: 5000

## 🎯 **Quick Deployment Steps**

### Step 1: Upload Files
1. Upload `machine-monitoring-webapp.zip` to your server (**************)
2. Extract to: `C:\inetpub\wwwroot\machine-monitoring\`

### Step 2: Deploy
**Right-click `start_production.bat`** → **"Run as administrator"**

### Step 3: Verify
Access: http://**************:5000/dashboard

## 🔧 **Production Start Files**

### Option 1: Production Server (Recommended)
```cmd
start_production.bat
```
- ✅ Configured for **************:5000
- ✅ Automatic firewall configuration
- ✅ Production-ready with Waitress
- ✅ Comprehensive health checks

### Option 2: Main Start File
```cmd
start.bat
```
- ✅ Updated with your server URLs
- ✅ Compatible package versions
- ✅ Database connection testing

### Option 3: Fixed Version
```cmd
start_fixed.bat
```
- ✅ Comprehensive error handling
- ✅ Step-by-step diagnostics

## 🌐 **Production URLs**

### For Users:
- **Main Dashboard**: http://**************:5000/dashboard
- **Login Page**: http://**************:5000/login
- **Health Check**: http://**************:5000/api/health

### For Arduino Integration:
- **Data Submission**: http://**************:5000/api/data
- **Server IP**: **************
- **Port**: 5000

## 🔑 **Login Credentials**

| Username | Password | Access |
|----------|----------|---------|
| admin | admin123 | Full Admin |
| pankaj | pankaj123 | Full Admin |
| abhi | abhi123 | Full Admin |
| ankur_admin | ankur@2023 | Full Admin |

## 🤖 **Arduino Configuration**

Update your Arduino code with these settings:

```cpp
// Production Server Configuration
String serverURL = "**************";
int serverPort = 5000;
String apiEndpoint = "/api/data";

// Complete URL for data submission
String fullURL = "http://**************:5000/api/data";
```

### Sample Arduino POST Request:
```cpp
// JSON data format
{
    "machine_id": "machine_001",
    "start_time": "2025-08-09T10:30:00",
    "end_time": "2025-08-09T10:35:00",
    "duration": 300
}
```

## 📊 **Expected Dashboard Features**

### Machine Selection:
- **machine_001**: 18 events
- **machine_002**: 4 events  
- **machine_003**: 3 events

### Real-time Data:
- ✅ Event counts update based on selected machine
- ✅ Live charts and analytics
- ✅ Runtime statistics
- ✅ Recent events display

## 🔥 **Firewall Configuration**

The production start file automatically configures:
```cmd
netsh advfirewall firewall add rule name="Machine Monitoring Port 5000" dir=in action=allow protocol=TCP localport=5000
```

### Manual Firewall Setup (if needed):
1. Open Windows Defender Firewall
2. Click "Advanced settings"
3. Click "Inbound Rules" → "New Rule"
4. Select "Port" → "TCP" → "Specific local ports: 5000"
5. Allow the connection
6. Apply to all profiles
7. Name: "Machine Monitoring Port 5000"

## 🧪 **Testing Production Deployment**

### Step 1: Health Check
Visit: http://**************:5000/api/health

Expected response:
```json
{
  "status": "healthy",
  "supabase_available": true,
  "database_records": 25,
  "database_status": "connected"
}
```

### Step 2: Dashboard Access
Visit: http://**************:5000/dashboard
- Should load without errors
- Machine dropdown should show machine_001, machine_002, machine_003
- Event counts should change when selecting different machines

### Step 3: API Testing
Test data submission:
```bash
curl -X POST http://**************:5000/api/data \
  -H "Content-Type: application/json" \
  -d '{"machine_id":"machine_001","start_time":"2025-08-09T10:30:00","end_time":"2025-08-09T10:35:00","duration":300}'
```

## 🚨 **Troubleshooting**

### Issue: Cannot access from external network
**Solution:**
1. Check Windows Firewall settings
2. Verify router port forwarding (if behind NAT)
3. Test local access first: http://localhost:5000

### Issue: 500 Internal Server Error
**Solution:**
1. Check server logs in `app/logs/`
2. Verify database connection: http://**************:5000/api/health
3. Restart with `start_production.bat`

### Issue: Arduino cannot submit data
**Solution:**
1. Test API endpoint manually with curl/Postman
2. Check Arduino network connectivity
3. Verify JSON format in Arduino code

## 📈 **Production Monitoring**

### Server Status:
- **Health Endpoint**: http://**************:5000/api/health
- **Log Files**: `C:\inetpub\wwwroot\machine-monitoring\app\logs\`
- **Process Monitoring**: Check for python.exe process

### Performance:
- **Expected Response Time**: < 500ms
- **Concurrent Users**: Supports multiple simultaneous users
- **Database**: Real-time Supabase connection

## 🎉 **Success Indicators**

✅ **Server accessible** at http://**************:5000
✅ **Dashboard loads** without errors
✅ **Login works** with admin/admin123
✅ **Machine filtering** shows correct event counts
✅ **Health endpoint** returns "healthy" status
✅ **Arduino can submit** data successfully
✅ **Real-time updates** work correctly

## 🔧 **Production Maintenance**

### Daily:
- Check health endpoint
- Monitor log files for errors
- Verify dashboard accessibility

### Weekly:
- Review database records count
- Check system resources (CPU, memory)
- Test Arduino data submission

### Monthly:
- Update dependencies if needed
- Review user access logs
- Backup configuration files

---

**🎯 Your Machine Monitoring System is now production-ready at http://**************:5000!** 🚀
