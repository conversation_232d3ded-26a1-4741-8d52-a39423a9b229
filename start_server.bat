@echo off
echo ========================================
echo   Machine Monitoring System Startup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

echo Python is available
echo.

REM Start the server
echo Starting Machine Monitoring System...
echo.
echo Available at:
echo   - http://localhost:5000
echo   - http://127.0.0.1:5000
echo   - http://0.0.0.0:5000
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

python start_server.py

echo.
echo Server stopped.
pause
