@echo off
title Testing Fixed Machine Monitoring App

echo ========================================
echo    Testing Fixed Machine Monitoring App
echo ========================================
echo.

REM Navigate to app directory
cd /d "%~dp0app"

REM Check if app.py exists
if not exist "app.py" (
    echo ERROR: app.py not found
    pause
    exit /b 1
)

echo [INFO] Found app.py
echo [INFO] Testing Supabase connection...

REM Test Supabase connection
python -c "from supabase import create_client; client = create_client('https://zvfltkbciwppawghqpdl.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE'); result = client.table('machine_events').select('*').limit(1).execute(); print('[SUCCESS] Supabase connection working!'); print(f'[INFO] Records found: {len(result.data) if result.data else 0}')"

if errorlevel 1 (
    echo [ERROR] Supabase connection failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] All tests passed!
echo [INFO] The app should now work correctly
echo.
echo To start the app, run: start.bat
echo.
pause
