# Deployment Checklist

## Pre-Deployment

- [ ] Python 3.8+ installed on server
- [ ] Internet connection available
- [ ] Port 5000 available (or configure different port)
- [ ] Sufficient disk space (minimum 500MB)
- [ ] Administrator privileges (for initial setup)

## Deployment Steps

1. **Extract Files**
   - [ ] Extract the zip file to desired location (e.g., `C:\machine-monitoring\`)
   - [ ] Ensure all files are present

2. **Initial Setup**
   - [ ] Double-click `start.bat`
   - [ ] Wait for virtual environment creation
   - [ ] Wait for dependency installation
   - [ ] Verify server starts successfully

3. **Test Access**
   - [ ] Open browser to `http://localhost:5000`
   - [ ] Login with admin/admin123
   - [ ] Verify dashboard loads
   - [ ] Test machine selection dropdown

4. **Network Access (if needed)**
   - [ ] Configure Windows Firewall to allow port 5000
   - [ ] Test access from other devices: `http://SERVER_IP:5000`

## Post-Deployment

### Security Configuration

- [ ] Change default admin password
- [ ] Review user accounts in `app.py`
- [ ] Configure HTTPS if needed
- [ ] Set up backup procedures

### Monitoring

- [ ] Check logs in `app/logs/` directory
- [ ] Monitor system resources
- [ ] Test Arduino data submission
- [ ] Verify data persistence

### Maintenance

- [ ] Set up automatic startup (optional)
- [ ] Configure log rotation
- [ ] Plan regular updates
- [ ] Document custom configurations

## Firewall Configuration

### Windows Firewall

1. Open Windows Defender Firewall
2. Click "Advanced settings"
3. Click "Inbound Rules" → "New Rule"
4. Select "Port" → "TCP" → "Specific local ports: 5000"
5. Allow the connection
6. Apply to all profiles
7. Name: "Machine Monitoring System"

### Alternative: Quick Command

Run as Administrator:
```cmd
netsh advfirewall firewall add rule name="Machine Monitoring" dir=in action=allow protocol=TCP localport=5000
```

## Troubleshooting

### Server Won't Start

1. Check Python installation: `python --version`
2. Check port availability: `netstat -an | find "5000"`
3. Run as Administrator
4. Check logs in `app/logs/`

### Can't Access from Network

1. Verify firewall settings
2. Check server IP address: `ipconfig`
3. Test local access first
4. Verify network connectivity

### Database Issues

1. Check internet connection
2. Verify Supabase service status
3. Check application logs
4. Test API endpoints manually

## Production Recommendations

### Performance

- [ ] Use production WSGI server (Waitress is included)
- [ ] Configure reverse proxy (nginx/Apache) if needed
- [ ] Set up load balancing for high traffic
- [ ] Monitor memory and CPU usage

### Security

- [ ] Enable HTTPS with SSL certificate
- [ ] Use environment variables for secrets
- [ ] Implement rate limiting
- [ ] Regular security updates

### Backup

- [ ] Database backup strategy
- [ ] Application file backup
- [ ] Configuration backup
- [ ] Recovery testing

## Support Contacts

- Technical Issues: Check application logs
- Arduino Integration: Refer to `final_code/final_code.ino`
- Database Issues: Check Supabase dashboard
