# Machine Monitoring System

A real-time machine monitoring dashboard built with Flask and Supabase, designed for Arduino-based machine monitoring.

## Features

- **Real-time Dashboard**: Monitor machine operations with live data updates
- **Machine Selection**: Filter data by specific machines (machine_001, machine_002, machine_003)
- **Data Visualization**: Charts for runtime trends, hourly distribution, and duration analysis
- **User Authentication**: Secure login system with role-based access
- **Event Tracking**: Detailed logging of machine start/stop events
- **Responsive Design**: Works on desktop and mobile devices

## Quick Start

### Prerequisites

- Python 3.8 or higher
- Internet connection (for Supabase database)

### Installation & Startup

1. **Extract the zip file** to your desired location
2. **Double-click `start.bat`** - This will automatically:
   - Create a virtual environment
   - Install all dependencies
   - Start the web server

3. **Access the dashboard**:
   - Open your browser and go to `http://localhost:5000`
   - Or access from network: `http://YOUR_SERVER_IP:5000`

### Default Login Credentials

- **Username**: `admin`
- **Password**: `admin123`

## Configuration

### Database Setup

The application is pre-configured to connect to Supabase. The database credentials are embedded in the application for easy deployment.

### Machine IDs

The system supports Arduino format machine IDs:
- `machine_001`
- `machine_002` 
- `machine_003`

### User Accounts

Default users included:
- `admin` / `admin123` - Full access to all machines
- `pankaj` / `pankaj123` - Admin access
- `abhi` / `abhi123` - Admin access
- `ankur_admin` / `ankur@2023` - Admin access

## API Endpoints

### Data Submission (for Arduino)

```
POST /api/data
Content-Type: application/json

{
    "machine_id": "machine_001",
    "start_time": "2025-08-09T10:30:00",
    "end_time": "2025-08-09T10:35:00",
    "duration": 300
}
```

### Dashboard Data

- `GET /api/summary?machine_id=machine_001` - Get summary for specific machine
- `GET /api/machine-data?machine_id=machine_001` - Get detailed events
- `GET /api/charts/daily-runtime?machine_id=machine_001` - Daily runtime chart data

## File Structure

```
machine-monitoring/
├── start.bat              # Main startup script
├── README.md              # This file
├── app/
│   ├── app.py            # Main Flask application
│   ├── requirements.txt   # Python dependencies
│   ├── templates/        # HTML templates
│   │   ├── dashboard.html
│   │   ├── events.html
│   │   ├── login.html
│   │   └── ...
│   └── static/           # CSS, JS, images
│       ├── css/
│       ├── js/
│       └── images/
└── final_code/
    └── final_code.ino    # Arduino code
```

## Troubleshooting

### Common Issues

1. **Port 5000 already in use**:
   - Stop other applications using port 5000
   - Or modify the port in `app.py` (line with `app.run(host='0.0.0.0', port=5000)`)

2. **Python not found**:
   - Install Python from https://python.org
   - Make sure to check "Add Python to PATH" during installation

3. **Dependencies installation fails**:
   - Check internet connection
   - Run `start.bat` as administrator

4. **Database connection issues**:
   - Check internet connection
   - Verify Supabase service status

### Logs

Application logs are stored in `app/logs/` directory.

## Production Deployment

For production deployment:

1. **Use a production WSGI server** (already included - Waitress)
2. **Set up reverse proxy** (nginx/Apache) if needed
3. **Configure firewall** to allow port 5000
4. **Set up SSL certificate** for HTTPS
5. **Configure environment variables** for sensitive data

## Support

For technical support or questions, please refer to the application logs or contact the development team.

## Version

Version 1.0 - Arduino Machine Monitoring System
