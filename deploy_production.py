#!/usr/bin/env python3
"""
Production deployment script for Machine Monitoring System
Deploys to your production server at **************
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def setup_production_environment():
    """Setup production environment"""
    print("🏭 Setting up Production Environment")
    print("=" * 50)
    
    # Production server paths
    production_path = Path("C:/inetpub/wwwroot/machine-monitoring")
    app_path = production_path / "app"
    
    # Create directories if they don't exist
    production_path.mkdir(parents=True, exist_ok=True)
    app_path.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ Production directory: {production_path}")
    print(f"✅ App directory: {app_path}")
    
    return production_path, app_path

def install_dependencies(production_path):
    """Install Python dependencies"""
    print("\n📦 Installing Dependencies")
    print("-" * 30)
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if requirements_file.exists():
        try:
            # Copy requirements.txt to production
            shutil.copy2(requirements_file, production_path)
            
            # Install packages
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', 
                str(production_path / "requirements.txt")
            ])
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    else:
        print("⚠️ requirements.txt not found, installing basic packages")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                'flask', 'supabase', 'python-dotenv'
            ])
            print("✅ Basic packages installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install basic packages: {e}")
            return False
    
    return True

def copy_application_files(app_path):
    """Copy application files to production"""
    print("\n📁 Copying Application Files")
    print("-" * 30)
    
    source_app_path = Path(__file__).parent / "app"
    
    # Files to copy
    files_to_copy = [
        "app_production.py",
        "templates",
        "static"
    ]
    
    for item in files_to_copy:
        source_item = source_app_path / item
        dest_item = app_path / item
        
        if source_item.exists():
            if source_item.is_file():
                shutil.copy2(source_item, dest_item)
                print(f"✅ Copied file: {item}")
            elif source_item.is_dir():
                if dest_item.exists():
                    shutil.rmtree(dest_item)
                shutil.copytree(source_item, dest_item)
                print(f"✅ Copied directory: {item}")
        else:
            print(f"⚠️ Source not found: {item}")
    
    return True

def create_startup_scripts(production_path, app_path):
    """Create startup scripts for production"""
    print("\n🚀 Creating Startup Scripts")
    print("-" * 30)
    
    # Create Python startup script
    startup_script = production_path / "start_production.py"
    startup_content = f'''#!/usr/bin/env python3
"""
Production startup script for Machine Monitoring System
"""

import os
import sys
from pathlib import Path

# Add app directory to Python path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

# Set environment variables
os.environ['FLASK_ENV'] = 'production'
os.environ['HOST'] = '0.0.0.0'
os.environ['PORT'] = '5000'
os.environ['DEBUG'] = 'False'

# Change to app directory
os.chdir(app_dir)

# Import and run the app
if __name__ == '__main__':
    try:
        from app_production import app
        print("🏭 Starting Machine Monitoring System (Production)")
        print("=" * 60)
        print("🌐 Server accessible at:")
        print("   ➤ http://**************:5000")
        print("   ➤ http://localhost:5000")
        print("⏹️  Press Ctrl+C to stop")
        print("=" * 60)
        
        app.run(host='0.0.0.0', port=5000, debug=False)
    except Exception as e:
        print(f"❌ Error starting application: {{e}}")
        input("Press Enter to exit...")
'''
    
    with open(startup_script, 'w') as f:
        f.write(startup_content)
    
    print(f"✅ Created startup script: {startup_script}")
    
    # Create batch file for Windows
    batch_script = production_path / "start_production.bat"
    batch_content = '''@echo off
echo ========================================
echo   Machine Monitoring System (Production)
echo ========================================
echo.
echo Starting production server...
echo Available at: http://**************:5000
echo.
python start_production.py
pause
'''
    
    with open(batch_script, 'w') as f:
        f.write(batch_content)
    
    print(f"✅ Created batch script: {batch_script}")
    
    return True

def create_service_script(production_path):
    """Create Windows service script"""
    print("\n🔧 Creating Service Configuration")
    print("-" * 30)
    
    # Create a simple service runner
    service_script = production_path / "run_as_service.py"
    service_content = '''#!/usr/bin/env python3
"""
Windows service runner for Machine Monitoring System
"""

import os
import sys
import time
import signal
from pathlib import Path

def signal_handler(sig, frame):
    print("\\nService stopping...")
    sys.exit(0)

def main():
    # Set up signal handler
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Add app directory to Python path
    app_dir = Path(__file__).parent / "app"
    sys.path.insert(0, str(app_dir))
    
    # Set environment variables
    os.environ['FLASK_ENV'] = 'production'
    os.environ['HOST'] = '0.0.0.0'
    os.environ['PORT'] = '5000'
    os.environ['DEBUG'] = 'False'
    
    # Change to app directory
    os.chdir(app_dir)
    
    try:
        from app_production import app
        print("🏭 Machine Monitoring Service Starting...")
        app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        print(f"❌ Service error: {e}")
        time.sleep(5)  # Wait before exit

if __name__ == '__main__':
    main()
'''
    
    with open(service_script, 'w') as f:
        f.write(service_content)
    
    print(f"✅ Created service script: {service_script}")
    
    return True

def main():
    """Main deployment function"""
    print("🚀 Machine Monitoring System - Production Deployment")
    print("=" * 60)
    
    try:
        # Setup production environment
        production_path, app_path = setup_production_environment()
        
        # Install dependencies
        if not install_dependencies(production_path):
            print("❌ Deployment failed at dependency installation")
            return False
        
        # Copy application files
        if not copy_application_files(app_path):
            print("❌ Deployment failed at file copying")
            return False
        
        # Create startup scripts
        if not create_startup_scripts(production_path, app_path):
            print("❌ Deployment failed at script creation")
            return False
        
        # Create service script
        if not create_service_script(production_path):
            print("❌ Deployment failed at service script creation")
            return False
        
        print("\n✅ Deployment Completed Successfully!")
        print("=" * 60)
        print("🏭 Production server deployed to:")
        print(f"   ➤ {production_path}")
        print()
        print("🚀 To start the server:")
        print(f"   ➤ Run: {production_path}/start_production.bat")
        print(f"   ➤ Or:  python {production_path}/start_production.py")
        print()
        print("🌐 Server will be accessible at:")
        print("   ➤ http://**************:5000")
        print("   ➤ http://localhost:5000")
        print()
        print("🔑 Login credentials:")
        print("   ➤ admin / admin123")
        print("   ➤ pankaj / pankaj123")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Deployment failed: {e}")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        input("Press Enter to continue...")
