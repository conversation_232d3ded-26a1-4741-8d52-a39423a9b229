../../Scripts/tests.exe,sha256=IVbtAS5MdXgpkYqs4_pI5TdRQzYAj4aFvKxmE407w8w,108450
supabase-2.7.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
supabase-2.7.4.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
supabase-2.7.4.dist-info/METADATA,sha256=P8i7Jmjm-7F-b1zl7vvKmpSae5bEuKFQ0Ze5vrGm7yc,10854
supabase-2.7.4.dist-info/RECORD,,
supabase-2.7.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
supabase-2.7.4.dist-info/WHEEL,sha256=sP946D7jFCHeNz5Iq4fL4Lu-PrWrFsgfLXbbkciIZwg,88
supabase-2.7.4.dist-info/entry_points.txt,sha256=F4onP9kSP0FoU2sCoOxrYbU-c60KwlZ_0quCskleaKg,50
supabase/__init__.py,sha256=1MssQ9SeSfdzxF8zngUdsYWAbccVBSM8pPgnnlqyJM0,1111
supabase/__pycache__/__init__.cpython-313.pyc,,
supabase/__pycache__/client.cpython-313.pyc,,
supabase/__pycache__/version.cpython-313.pyc,,
supabase/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
supabase/_async/__pycache__/__init__.cpython-313.pyc,,
supabase/_async/__pycache__/auth_client.cpython-313.pyc,,
supabase/_async/__pycache__/client.cpython-313.pyc,,
supabase/_async/auth_client.py,sha256=8kG2bcwJfyrTMGBjGRQrU1RJ0wKVr27y_6dmmt9uqZM,1187
supabase/_async/client.py,sha256=h00zLFvna9mpdWnO9qVtUN4bmqgDgL6E7YxivTggL74,11422
supabase/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
supabase/_sync/__pycache__/__init__.cpython-313.pyc,,
supabase/_sync/__pycache__/auth_client.cpython-313.pyc,,
supabase/_sync/__pycache__/client.cpython-313.pyc,,
supabase/_sync/auth_client.py,sha256=yJa8gJSjmqQp6fOGG4M1EzWdVS84-FJ0A9SDzMCYHw8,1177
supabase/_sync/client.py,sha256=BPhIh5VnpdWh26sioX4KGqrcFjLVpFlQwR_Jq4C7cCk,11345
supabase/client.py,sha256=8dZxf5QNCnJ7SSe8VVgpsKtQ3YphkUatOYBk6Q-j3t8,1082
supabase/lib/__init__.py,sha256=hBGVFLg5RVk6liHGIUuak1crNBiz5m-mPvvdxv8nmNU,67
supabase/lib/__pycache__/__init__.cpython-313.pyc,,
supabase/lib/__pycache__/client_options.cpython-313.pyc,,
supabase/lib/client_options.py,sha256=u0Ieq5UI0e_FwIs160MLNFk1wnH1d_6mnUTWQcFfdkw,3364
supabase/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
supabase/version.py,sha256=EoOPVm7t2gfI6HeP6RHrc1UNn1CkLXVpze5dTmVALvg,52
